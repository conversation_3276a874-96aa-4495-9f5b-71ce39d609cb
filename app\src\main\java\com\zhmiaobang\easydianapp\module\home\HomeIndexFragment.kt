package com.zhmiaobang.easydianapp.module.home

import androidx.fragment.app.viewModels
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.FragmentHomeIndexBinding
import com.zhmiaobang.easydianapp.module.video.VideoRecordActivity

class HomeIndexFragment : Fragment() {

    companion object {
        fun newInstance() = HomeIndexFragment()
    }

    private val viewModel: HomeIndexViewModel by viewModels()
    private var _binding: FragmentHomeIndexBinding? = null
    private val binding get() = _binding!!

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // TODO: Use the ViewModel
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeIndexBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupClickListeners()
    }

    /**
     * 设置点击事件监听器
     */
    private fun setupClickListeners() {
        // 样本视频卡片点击事件
        binding.hIndexFuncSampleVideoCard.setOnClickListener {
            val intent = Intent(requireContext(), VideoRecordActivity::class.java)
            startActivity(intent)
        }

        // 其他功能卡片的点击事件可以在这里添加
        // binding.hIndexFuncShrimpCountCard.setOnClickListener { ... }
        // binding.hIndexFuncLengthMeasureCard.setOnClickListener { ... }
        // binding.hIndexFuncWaterTestCard.setOnClickListener { ... }
        // binding.hIndexFuncFeedingManageCard.setOnClickListener { ... }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}