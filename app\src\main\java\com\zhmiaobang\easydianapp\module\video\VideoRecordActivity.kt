package com.zhmiaobang.easydianapp.module.video

import android.Manifest
import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.AspectRatio
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.FocusMeteringAction
import androidx.camera.core.Preview
import androidx.camera.core.resolutionselector.AspectRatioStrategy
import androidx.camera.core.resolutionselector.ResolutionSelector

import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.video.MediaStoreOutputOptions
import androidx.camera.video.Quality
import androidx.camera.video.QualitySelector
import androidx.camera.video.Recorder
import androidx.camera.video.Recording
import androidx.camera.video.RecordingStats
import androidx.camera.video.VideoCapture
import androidx.camera.video.VideoRecordEvent
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ActivityVideoRecordBinding
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * 视频录制Activity - 基于CameraX实现无声视频录制
 *
 * 功能特性：
 * - 无声视频录制（完全禁用音频）
 * - 录制时长选择：5秒和10秒
 * - 视频比例选择：3:4和9:16
 * - 清晰度选择：720p、1080p、4K
 * - 自动保存到系统相册
 * - 触摸对焦功能
 * - 实时录制进度显示
 */
class VideoRecordActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "VideoRecordActivity"
        private const val FILENAME_PREFIX = "VIDEO_"
        private const val FILENAME_DATE_FORMAT = "yyyyMMdd_HHmmss"
    }

    // 录制状态枚举
    enum class RecordingState {
        IDLE,       // 空闲状态
        RECORDING,  // 录制中
        PAUSED      // 暂停状态
    }

    // 录制时长枚举
    enum class VideoDuration(val seconds: Int, val displayName: String) {
        FIVE_SECONDS(5, "5秒"),
        TEN_SECONDS(10, "10秒")
    }

    // 视频比例枚举
    enum class VideoRatio(val aspectRatio: Int, val displayName: String) {
        RATIO_3_4(AspectRatio.RATIO_4_3, "3:4"),
        RATIO_9_16(AspectRatio.RATIO_16_9, "9:16")
    }

    // 视频质量枚举
    enum class VideoQuality(val quality: Quality, val displayName: String) {
        HD_720P(Quality.HD, "720p"),
        FHD_1080P(Quality.FHD, "1080p"),
        UHD_4K(Quality.UHD, "4K")
    }

    // ViewBinding
    private lateinit var binding: ActivityVideoRecordBinding

    // 相机相关
    private var cameraProvider: ProcessCameraProvider? = null
    private var videoCapture: VideoCapture<Recorder>? = null
    private var camera: Camera? = null
    private var recording: Recording? = null
    private val cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()

    // 录制状态
    private var recordingState = RecordingState.IDLE
    private var selectedDuration = VideoDuration.FIVE_SECONDS
    private var selectedRatio = VideoRatio.RATIO_3_4
    private var selectedQuality = VideoQuality.FHD_1080P

    // 计时器
    private var recordingTimer: CountDownTimer? = null
    private var recordingStartTime: Long = 0

    // 权限管理
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            initializeCamera()
        } else {
            showError("需要相机权限才能录制视频")
            finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 初始化ViewBinding
        binding = ActivityVideoRecordBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 设置系统栏
        setupSystemBars()

        // 设置Toolbar
        setupToolbar()

        // 初始化UI组件
        setupUI()

        // 设置初始预览比例
        updatePreviewRatio()

        // 检查权限并初始化相机
        checkPermissions()
    }

    /**
     * 设置系统栏
     */
    private fun setupSystemBars() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.main) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }

    /**
     * 设置Toolbar
     */
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = "视频录制"
        }

        // 设置导航按钮点击事件
        binding.toolbar.setNavigationOnClickListener {
            onBackPressed()
        }
    }

    /**
     * 初始化UI组件
     */
    private fun setupUI() {
        setupRecordButton()
        updateUI()
    }

    /**
     * 创建选项菜单
     */
    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.video_record_menu, menu)
        updateMenuTitles(menu)
        return true
    }

    /**
     * 处理菜单项选择
     */
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        // 录制中禁用菜单选项
        if (recordingState == RecordingState.RECORDING) {
            showError("录制中无法修改设置")
            return true
        }

        return when (item.itemId) {
            // 录制时长选择
            R.id.menu_duration_5s -> {
                selectedDuration = VideoDuration.FIVE_SECONDS
                updateMenuTitles()
                Log.d(TAG, "选择录制时长: ${selectedDuration.displayName}")
                true
            }
            R.id.menu_duration_10s -> {
                selectedDuration = VideoDuration.TEN_SECONDS
                updateMenuTitles()
                Log.d(TAG, "选择录制时长: ${selectedDuration.displayName}")
                true
            }

            // 视频比例选择
            R.id.menu_ratio_3_4 -> {
                selectedRatio = VideoRatio.RATIO_3_4
                updateMenuTitles()
                updatePreviewRatio()
                Log.d(TAG, "选择视频比例: ${selectedRatio.displayName}")
                true
            }
            R.id.menu_ratio_9_16 -> {
                selectedRatio = VideoRatio.RATIO_9_16
                updateMenuTitles()
                updatePreviewRatio()
                Log.d(TAG, "选择视频比例: ${selectedRatio.displayName}")
                true
            }

            // 清晰度选择
            R.id.menu_quality_720p -> {
                selectedQuality = VideoQuality.HD_720P
                updateMenuTitles()
                rebindCamera()
                Log.d(TAG, "选择视频质量: ${selectedQuality.displayName}")
                true
            }
            R.id.menu_quality_1080p -> {
                selectedQuality = VideoQuality.FHD_1080P
                updateMenuTitles()
                rebindCamera()
                Log.d(TAG, "选择视频质量: ${selectedQuality.displayName}")
                true
            }
            R.id.menu_quality_4k -> {
                selectedQuality = VideoQuality.UHD_4K
                updateMenuTitles()
                rebindCamera()
                Log.d(TAG, "选择视频质量: ${selectedQuality.displayName}")
                true
            }

            else -> super.onOptionsItemSelected(item)
        }
    }

    /**
     * 更新菜单标题显示当前选中值
     */
    private fun updateMenuTitles(menu: Menu? = null) {
        val currentMenu = menu ?: binding.toolbar.menu

        currentMenu.findItem(R.id.menu_duration)?.title = "时长: ${selectedDuration.displayName}"
        currentMenu.findItem(R.id.menu_ratio)?.title = "比例: ${selectedRatio.displayName}"
        currentMenu.findItem(R.id.menu_quality)?.title = "清晰度: ${selectedQuality.displayName}"
    }

    /**
     * 更新PreviewView比例
     */
    private fun updatePreviewRatio() {
        // 动态调整PreviewView的宽高比
        val previewView = binding.videoPreview
        val layoutParams = previewView.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams

        // 根据选择的比例设置PreviewView的宽高比
        when (selectedRatio) {
            VideoRatio.RATIO_3_4 -> {
                // 3:4 比例 (宽:高 = 3:4)
                layoutParams.dimensionRatio = "3:4"
            }
            VideoRatio.RATIO_9_16 -> {
                // 9:16 比例 (宽:高 = 9:16)
                layoutParams.dimensionRatio = "9:16"
            }
        }

        previewView.layoutParams = layoutParams

        // 重新绑定相机以应用新比例
        if (cameraProvider != null) {
            bindCameraUseCases()
        }
    }

    /**
     * 重新绑定相机（用于质量变更）
     */
    private fun rebindCamera() {
        if (cameraProvider != null) {
            bindCameraUseCases()
        }
    }

    /**
     * 设置录制按钮
     */
    private fun setupRecordButton() {
        binding.recordButton.setOnClickListener {
            when (recordingState) {
                RecordingState.IDLE -> startRecording()
                RecordingState.RECORDING -> stopRecording()
                RecordingState.PAUSED -> resumeRecording()
            }
        }
    }

    /**
     * 检查权限
     */
    private fun checkPermissions() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
            == PackageManager.PERMISSION_GRANTED) {
            initializeCamera()
        } else {
            permissionLauncher.launch(Manifest.permission.CAMERA)
        }
    }

    /**
     * 初始化相机
     */
    private fun initializeCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)

        cameraProviderFuture.addListener({
            try {
                cameraProvider = cameraProviderFuture.get()
                bindCameraUseCases()
            } catch (exc: Exception) {
                Log.e(TAG, "相机初始化失败", exc)
                showError("相机初始化失败: ${exc.message}")
            }
        }, ContextCompat.getMainExecutor(this))
    }

    /**
     * 绑定相机用例
     */
    private fun bindCameraUseCases() {
        val cameraProvider = this.cameraProvider ?: return

        // 创建Preview用例
        val preview = createPreview()

        // 创建VideoCapture用例
        setupVideoCapture()

        // 选择后置摄像头
        val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

        try {
            // 解绑所有用例
            cameraProvider.unbindAll()

            // 绑定用例到相机
            camera = cameraProvider.bindToLifecycle(
                this,
                cameraSelector,
                preview,
                videoCapture
            )

            // 设置触摸对焦
            setupTouchFocus()

        } catch (exc: Exception) {
            Log.e(TAG, "用例绑定失败", exc)
            showError("相机绑定失败: ${exc.message}")
        }
    }

    /**
     * 创建Preview用例
     */
    @Suppress("DEPRECATION")
    private fun createPreview(): Preview {
        return Preview.Builder()
            .setTargetAspectRatio(selectedRatio.aspectRatio)
            .build()
            .also { it.setSurfaceProvider(binding.videoPreview.surfaceProvider) }
    }

    /**
     * 配置VideoCapture
     */
    @SuppressLint("RestrictedApi")
    private fun setupVideoCapture() {
        val recorder = createRecorder()
        videoCapture = VideoCapture.Builder(recorder)
            .build()
    }

    /**
     * 创建Recorder
     */
    private fun createRecorder(): Recorder {
        val qualitySelector = QualitySelector.from(selectedQuality.quality)
        return Recorder.Builder()
            .setQualitySelector(qualitySelector)
            .build()
    }

    /**
     * 设置触摸对焦
     */
    @SuppressLint("ClickableViewAccessibility")
    private fun setupTouchFocus() {
        binding.videoPreview.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                val factory = binding.videoPreview.meteringPointFactory
                val point = factory.createPoint(event.x, event.y)
                val action = FocusMeteringAction.Builder(point).build()

                camera?.cameraControl?.startFocusAndMetering(action)
                Log.d(TAG, "触摸对焦: (${event.x}, ${event.y})")
            }
            true
        }
    }

    /**
     * 开始录制
     */
    private fun startRecording() {
        val videoCapture = this.videoCapture ?: return

        // 创建输出选项
        val outputOptions = createOutputOptions()

        // 准备录制（注意：不调用withAudioEnabled()来禁用音频）
        val pendingRecording = videoCapture.output
            .prepareRecording(this, outputOptions)
            // 关键：这里故意不调用.withAudioEnabled()来实现无声录制

        // 开始录制
        recording = pendingRecording.start(
            ContextCompat.getMainExecutor(this),
            ::handleRecordingEvent
        )

        Log.d(TAG, "开始录制 - 时长: ${selectedDuration.displayName}, 比例: ${selectedRatio.displayName}, 质量: ${selectedQuality.displayName}")
    }

    /**
     * 停止录制
     */
    private fun stopRecording() {
        recording?.stop()
        recording = null
        recordingTimer?.cancel()
        recordingTimer = null
        Log.d(TAG, "停止录制")
    }

    /**
     * 恢复录制
     */
    private fun resumeRecording() {
        recording?.resume()
        Log.d(TAG, "恢复录制")
    }

    /**
     * 创建输出选项
     */
    private fun createOutputOptions(): MediaStoreOutputOptions {
        val contentValues = ContentValues().apply {
            put(MediaStore.Video.Media.DISPLAY_NAME, "${FILENAME_PREFIX}${System.currentTimeMillis()}")
            put(MediaStore.Video.Media.MIME_TYPE, "video/mp4")
            put(MediaStore.Video.Media.RELATIVE_PATH, Environment.DIRECTORY_MOVIES)
        }

        return MediaStoreOutputOptions.Builder(
            contentResolver,
            MediaStore.Video.Media.EXTERNAL_CONTENT_URI
        ).setContentValues(contentValues).build()
    }

    /**
     * 处理录制事件
     */
    private fun handleRecordingEvent(event: VideoRecordEvent) {
        when (event) {
            is VideoRecordEvent.Start -> {
                recordingState = RecordingState.RECORDING
                recordingStartTime = System.currentTimeMillis()
                updateUI()
                startRecordingTimer()
                Log.d(TAG, "录制开始")
            }

            is VideoRecordEvent.Finalize -> {
                recordingState = RecordingState.IDLE
                recordingTimer?.cancel()

                if (event.hasError()) {
                    handleRecordingError(event)
                } else {
                    handleRecordingSuccess(event)
                }
                updateUI()
            }

            is VideoRecordEvent.Status -> {
                // 更新录制统计信息
                updateRecordingStats(event.recordingStats)
            }

            is VideoRecordEvent.Pause -> {
                recordingState = RecordingState.PAUSED
                updateUI()
                Log.d(TAG, "录制暂停")
            }

            is VideoRecordEvent.Resume -> {
                recordingState = RecordingState.RECORDING
                updateUI()
                Log.d(TAG, "录制恢复")
            }
        }
    }

    /**
     * 开始录制计时器
     */
    private fun startRecordingTimer() {
        val totalDurationMs = selectedDuration.seconds * 1000L

        recordingTimer = object : CountDownTimer(totalDurationMs, 100L) {
            override fun onTick(millisUntilFinished: Long) {
                val elapsedMs = totalDurationMs - millisUntilFinished
                updateRecordingProgress(elapsedMs, totalDurationMs)
            }

            override fun onFinish() {
                // 时间到，自动停止录制
                stopRecording()
            }
        }.start()
    }

    /**
     * 更新录制进度
     */
    private fun updateRecordingProgress(elapsedMs: Long, totalMs: Long) {
        val progress = (elapsedMs.toFloat() / totalMs * 100).toInt()
        val remainingSeconds = (totalMs - elapsedMs) / 1000

        binding.recordingProgress.progress = progress
        binding.timeText.text = String.format("%02d:%02d",
            remainingSeconds / 60, remainingSeconds % 60)
    }

    /**
     * 更新录制统计信息
     */
    private fun updateRecordingStats(stats: RecordingStats) {
        val durationSeconds = stats.recordedDurationNanos / 1_000_000_000
        val sizeBytes = stats.numBytesRecorded

        Log.d(TAG, "录制统计 - 时长: ${durationSeconds}s, 大小: ${sizeBytes}bytes")
    }

    /**
     * 处理录制错误
     */
    private fun handleRecordingError(event: VideoRecordEvent.Finalize) {
        val errorMsg = when (event.error) {
            VideoRecordEvent.Finalize.ERROR_INSUFFICIENT_STORAGE -> "存储空间不足"
            VideoRecordEvent.Finalize.ERROR_ENCODING_FAILED -> "视频编码失败"
            VideoRecordEvent.Finalize.ERROR_RECORDER_ERROR -> "录制器错误"
            VideoRecordEvent.Finalize.ERROR_FILE_SIZE_LIMIT_REACHED -> "文件大小超出限制"
            VideoRecordEvent.Finalize.ERROR_INVALID_OUTPUT_OPTIONS -> "输出选项无效"
            VideoRecordEvent.Finalize.ERROR_SOURCE_INACTIVE -> "视频源不活跃"
            VideoRecordEvent.Finalize.ERROR_NO_VALID_DATA -> "没有有效数据"
            VideoRecordEvent.Finalize.ERROR_DURATION_LIMIT_REACHED -> "录制时长超出限制"
            else -> "录制失败: ${event.cause?.message ?: "未知错误"}"
        }

        Log.e(TAG, "录制错误: $errorMsg", event.cause)
        showError(errorMsg)
    }

    /**
     * 处理录制成功
     */
    private fun handleRecordingSuccess(event: VideoRecordEvent.Finalize) {
        val outputUri = event.outputResults.outputUri
        Log.d(TAG, "录制成功，保存到: $outputUri")
        showSuccess("视频已保存到相册")
    }

    /**
     * 更新UI状态
     */
    private fun updateUI() {
        when (recordingState) {
            RecordingState.IDLE -> {
                binding.recordButton.text = "开始录制"
                binding.recordButton.isEnabled = true
                binding.recordingProgress.visibility = View.GONE
                binding.statusText.text = "准备录制"
                binding.timeText.visibility = View.GONE
            }

            RecordingState.RECORDING -> {
                binding.recordButton.text = "停止录制"
                binding.recordButton.isEnabled = true
                binding.recordingProgress.visibility = View.VISIBLE
                binding.statusText.text = "录制中..."
                binding.timeText.visibility = View.VISIBLE
            }

            RecordingState.PAUSED -> {
                binding.recordButton.text = "继续录制"
                binding.recordButton.isEnabled = true
                binding.statusText.text = "录制暂停"
            }
        }

        // 更新菜单标题
        updateMenuTitles()
    }

    /**
     * 显示错误信息
     */
    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }

    /**
     * 显示成功信息
     */
    private fun showSuccess(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    /**
     * 生命周期管理 - 暂停时停止录制
     */
    override fun onPause() {
        super.onPause()
        if (recordingState == RecordingState.RECORDING) {
            stopRecording()
        }
    }

    /**
     * 生命周期管理 - 销毁时释放资源
     */
    override fun onDestroy() {
        super.onDestroy()
        recording?.close()
        recordingTimer?.cancel()
        cameraExecutor.shutdown()
    }

    /**
     * 处理返回按键
     */
    override fun onBackPressed() {
        if (recordingState == RecordingState.RECORDING) {
            showError("正在录制中，请先停止录制")
            return
        }
        super.onBackPressed()
    }
}