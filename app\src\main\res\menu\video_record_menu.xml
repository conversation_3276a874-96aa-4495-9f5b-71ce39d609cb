<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- 录制时长选择 -->
    <item
        android:id="@+id/menu_duration"
        android:icon="@drawable/ic_timer"
        android:title="时长: 5秒"
        app:showAsAction="ifRoom">
        <menu>
            <item
                android:id="@+id/menu_duration_5s"
                android:title="5秒"
                android:checkable="true"
                android:checked="true" />
            <item
                android:id="@+id/menu_duration_10s"
                android:title="10秒"
                android:checkable="true" />
        </menu>
    </item>

    <!-- 视频比例选择 -->
    <item
        android:id="@+id/menu_ratio"
        android:icon="@drawable/ic_aspect_ratio"
        android:title="比例: 3:4"
        app:showAsAction="ifRoom">
        <menu>
            <item
                android:id="@+id/menu_ratio_3_4"
                android:title="3:4 (竖屏)"
                android:checkable="true"
                android:checked="true" />
            <item
                android:id="@+id/menu_ratio_9_16"
                android:title="9:16 (超宽竖屏)"
                android:checkable="true" />
        </menu>
    </item>

    <!-- 清晰度选择 -->
    <item
        android:id="@+id/menu_quality"
        android:icon="@drawable/ic_hd"
        android:title="清晰度: 1080p"
        app:showAsAction="ifRoom">
        <menu>
            <item
                android:id="@+id/menu_quality_720p"
                android:title="720p (HD)"
                android:checkable="true" />
            <item
                android:id="@+id/menu_quality_1080p"
                android:title="1080p (FHD)"
                android:checkable="true"
                android:checked="true" />
            <item
                android:id="@+id/menu_quality_4k"
                android:title="4K (UHD)"
                android:checkable="true" />
        </menu>
    </item>

</menu>
